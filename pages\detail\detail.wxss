/* pages/detail/detail.wxss */
.header {
  padding: 40rpx 30rpx;
  text-align: center;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  margin: 20rpx 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.username {
  font-size: 28rpx;
  color: #666;
}

.password-mode {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15rpx;
  padding: 10rpx 20rpx;
  background-color: #e8f4fd;
  border-radius: 20rpx;
  border: 1rpx solid #007AFF;
}

.mode-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.mode-text {
  font-size: 24rpx;
  color: #007AFF;
  font-weight: 500;
}

.password-info {
  padding: 0 30rpx;
  margin-top: 40rpx;
}

.gesture-container {
  margin: 20rpx 0 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.gesture-title {
  font-size: 32rpx;
  margin-bottom: 30rpx;
  text-align: center;
  color: #333;
}

.gesture-canvas {
  width: 600rpx;
  height: 600rpx;
  background-color: #fafafa;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.gesture-hint {
  color: #999;
  font-size: 24rpx;
  margin-top: 10rpx;
}

.password-section {
  margin: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.display-password {
  background-color: #f0f0f0;
  padding: 30rpx;
  border-radius: 8rpx;
  margin-bottom: 30rpx;
  font-family: monospace;
  font-size: 34rpx;
  letter-spacing: 2rpx;
  width: 100%;
  text-align: center;
  word-break: break-all;
  box-shadow: inset 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.password-actions {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-top: 20rpx;
}

.btn-action {
  background-color: #007AFF;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 0;
  text-align: center;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  width: 100%;
}

.btn-reset {
  background-color: #f8f8f8;
  color: #333;
}

.delete-btn {
  margin: 60rpx auto 40rpx;
  background-color: #FF3B30;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 0;
  text-align: center;
  width: 90%;
  font-size: 32rpx;
}
