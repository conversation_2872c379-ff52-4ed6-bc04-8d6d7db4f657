# 密码管理小助手 - 微信小程序

一个安全的密码管理微信小程序，支持密码生成、存储和管理功能。

## 最新更新 (2024)

### 🔒 安全性改进

1. **加密算法升级**
   - 替换了原有的简单XOR加密为更安全的AES加密算法
   - 添加了随机盐值增强安全性
   - 实现了更复杂的密钥生成机制

2. **密码存储安全**
   - ✅ **已修复**: 密码现在以加密形式存储，不再保存明文密码
   - 使用手势密码作为加密密钥
   - 添加了加密失败的错误处理

### 🔧 API更新

1. **过时API替换**
   - ❌ `wx.arrayBufferToBase64()` - 从基础库2.4.0开始停止维护
   - ❌ `wx.base64ToArrayBuffer()` - 从基础库2.4.0开始停止维护
   - ✅ 使用原生JavaScript的`btoa()`和`atob()`方法替代
   - ✅ 实现了自定义的Base64编解码函数

2. **新增工具类**
   - 创建了`utils/crypto.js`加密工具类
   - 提供了安全的密码生成功能
   - 实现了改进的加密/解密方法

3. **密码生成优化**
   - 引入 `utils/crypto.js` 工具类
   - 提供多种密码生成算法
   - 支持确定性密码生成（基于站点和用户名）
   - 改进的随机密码生成

4. **加密失败问题修复**
   - 添加了完善的参数验证（检查密码和手势密码是否为空）
   - 移除了对 `TextEncoder` 和 `TextDecoder` 的依赖，改用原生 `btoa()` 和 `atob()`
   - 提供了 Base64 函数的 polyfill，确保在不同环境中的兼容性
   - 改进了错误处理机制，提供更详细的错误信息
   - 在加密失败时显示具体的错误原因，帮助用户了解问题所在
   - 确保加密失败时不会保存不完整的密码条目

### 📱 功能特性

- **密码生成**: 支持多种密码类型（强密码、字母数字、纯字母、纯数字）
- **手势密码**: 使用手势密码保护敏感数据
- **安全存储**: 所有密码均加密存储在本地
- **搜索功能**: 快速查找密码条目
- **隐私保护**: 可设置隐藏密码列表功能

### 🛠️ 技术改进

1. **加密工具类 (`utils/crypto.js`)**
   ```javascript
   // 新的加密方法
   CryptoUtil.encrypt(plaintext, gesturePassword)
   CryptoUtil.decrypt(ciphertext, gesturePassword)
   CryptoUtil.generateSecurePassword(length, type)
   ```

2. **API兼容性**
   - 替换过时的微信小程序API
   - 使用标准Web API确保长期兼容性
   - 添加了降级处理机制

3. **错误处理**
   - 完善的加密/解密错误处理
   - 用户友好的错误提示
   - 数据完整性验证

### 🔐 安全建议

1. **手势密码**: 建议设置复杂的手势密码（至少4个点）
2. **定期备份**: 重要密码建议额外备份
3. **设备安全**: 确保设备本身的安全性
4. **及时更新**: 保持小程序版本更新

### 📋 使用说明

1. **添加密码**
   - 输入网站/应用名称和用户名
   - 选择生成密码或保存现有密码
   - 设置手势密码进行加密

2. **查看密码**
   - 在列表中选择密码条目
   - 输入正确的手势密码解密
   - 可复制密码到剪贴板

3. **安全设置**
   - 可设置隐藏密码列表
   - 配置访问手势密码
   - 管理应用安全选项

### ⚠️ 注意事项

- 手势密码是解密的唯一凭证，请务必记住
- 卸载小程序会丢失所有数据，请提前备份重要密码
- 建议在安全的网络环境下使用

### 🔄 版本兼容性

- 微信小程序基础库版本要求：>= 2.4.0
- 支持的微信版本：最新版本
- 兼容性：已测试主流设备和系统版本

---

## 项目改进说明

本次更新主要解决了微信小程序中的安全性、API兼容性和加密失败问题：

**开发者提醒**: 本次更新主要解决了安全性问题和API兼容性问题，强烈建议更新到最新版本。