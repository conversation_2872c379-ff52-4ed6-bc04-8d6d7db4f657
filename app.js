App({
  globalData: {
    userInfo: null
  },
  onLaunch: function() {
    // Initialize app
    console.log('App launched');
    
    // Initialize the password storage if it doesn't exist yet
    try {
      const passwordEntries = wx.getStorageSync('passwordEntries');
      if (!passwordEntries) {
        wx.setStorageSync('passwordEntries', []);
      }
    } catch (e) {
      console.error('Failed to initialize storage:', e);
      wx.setStorageSync('passwordEntries', []);
    }
  }
});
