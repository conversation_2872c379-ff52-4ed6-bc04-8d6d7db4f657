/* pages/settings/settings.wxss */
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  padding: 30rpx 0;
  text-align: center;
  background-color: white;
  margin-bottom: 20rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.settings-container {
  padding: 0 30rpx;
}

.settings-section {
  background-color: white;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  flex: 1;
  margin-right: 20rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.label-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.btn-secondary {
  background-color: #f8f8f8;
  color: #333;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  border: none;
}

.btn-primary {
  background-color: #007AFF;
  color: white;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  border: none;
}

/* 手势密码弹窗样式 */
.gesture-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 12rpx;
  width: 90%;
  max-width: 600rpx;
  padding: 40rpx;
}

.modal-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.modal-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.gesture-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 30rpx 0;
}

.gesture-canvas {
  width: 400rpx;
  height: 400rpx;
  background-color: #fafafa;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.error-message {
  color: #FF3B30;
  font-size: 28rpx;
  margin: 20rpx 0;
  text-align: center;
}

.success-message {
  color: #34C759;
  font-size: 28rpx;
  margin: 20rpx 0;
  text-align: center;
}

.gesture-hint {
  color: #999;
  font-size: 24rpx;
  margin-top: 10rpx;
  text-align: center;
}

.modal-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}

.modal-actions button {
  flex: 1;
  margin: 0 10rpx;
}
