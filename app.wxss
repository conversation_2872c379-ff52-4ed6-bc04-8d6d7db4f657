/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
} 

/* Common styles */
.page {
  padding: 20rpx;
}

.form-group {
  margin-bottom: 20rpx;
}

.input-item {
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.btn-primary {
  background-color: #007AFF;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 0;
  text-align: center;
  margin-top: 20rpx;
}

.btn-secondary {
  background-color: #f8f8f8;
  color: #333;
  border-radius: 8rpx;
  padding: 20rpx 0;
  text-align: center;
  margin-top: 20rpx;
}

.password-item {
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.password-item-info {
  flex: 1;
}

.site-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.username {
  font-size: 28rpx;
  color: #666;
}

.gesture-container {
  margin: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.gesture-title {
  font-size: 32rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.display-password {
  background-color: #f0f0f0;
  padding: 30rpx;
  border-radius: 8rpx;
  margin: 30rpx 0;
  font-family: monospace;
  font-size: 34rpx;
  letter-spacing: 2rpx;
  text-align: center;
  word-break: break-all;
}
