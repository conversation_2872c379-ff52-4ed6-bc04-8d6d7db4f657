// pages/index/index.js
Page({
  data: {
    passwordEntries: [],
    allPasswordEntries: [],
    searchText: '',
    needAuth: false,
    isAuthenticated: false,
    authTimestamp: 0, // 认证时间戳
    authValidDuration: 60000, // 60秒有效期

    // 手势密码认证相关
    authGesturePassword: [],
    lastPoint: null,
    currentLineColor: '#007AFF',
    gridSize: 3,
    pointRadius: 20,
    canvasWidth: 250,
    canvasHeight: 250,
    points: [],
    drawnLines: [],
    showAuthError: false,
    authErrorMessage: ''
  },

  onLoad: function(options) {
    this.checkAuthRequirement();
    this.initCanvas();
  },

  onShow: function() {
    this.checkAuthRequirement();
    if (!this.data.needAuth || this.data.isAuthenticated) {
      this.loadPasswordEntries();
    }
  },

  onReady: function() {
    // Canvas will be initialized when needed
  },

  checkAuthRequirement: function() {
    try {
      const settings = wx.getStorageSync('appSettings') || {};
      const needAuth = settings.hidePasswordList && settings.accessGesturePassword && settings.accessGesturePassword.length > 0;
      
      // 检查是否在60秒有效期内
      const currentTime = Date.now();
      const isWithinValidPeriod = (currentTime - this.data.authTimestamp) < this.data.authValidDuration;
      
      this.setData({
        needAuth: needAuth,
        isAuthenticated: !needAuth || (needAuth && isWithinValidPeriod)
      });

      if (needAuth && !this.data.isAuthenticated) {
        // 清理手势痕迹
        this.clearGestureTraces();
        // Initialize canvas for authentication
        setTimeout(() => {
          this.initAuthCanvas();
        }, 100);
      }
    } catch (e) {
      console.error('Failed to check auth requirement:', e);
      this.setData({ needAuth: false, isAuthenticated: true });
    }
  },

  loadPasswordEntries: function() {
    try {
      const passwordEntries = wx.getStorageSync('passwordEntries') || [];
      this.setData({
        passwordEntries: passwordEntries,
        allPasswordEntries: passwordEntries
      });
    } catch (e) {
      console.error('Failed to load password entries:', e);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  handleSearchInput: function(e) {
    const searchText = e.detail.value.trim().toLowerCase();
    this.setData({ searchText });

    if (!searchText) {
      // If search is empty, show all entries
      this.setData({ passwordEntries: this.data.allPasswordEntries });
      return;
    }

    // Filter password entries based on search text
    const filteredEntries = this.data.allPasswordEntries.filter(entry => {
      return entry.site.toLowerCase().includes(searchText) ||
             (entry.username || '').toLowerCase().includes(searchText);
    });

    this.setData({ passwordEntries: filteredEntries });
  },

  clearSearch: function() {
    this.setData({
      searchText: '',
      passwordEntries: this.data.allPasswordEntries
    });
  },

  navigateToAdd: function() {
    wx.navigateTo({
      url: '/pages/add/add'
    });
  },

  navigateToDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    });
  },

  navigateToSettings: function() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },

  initCanvas: function() {
    // Calculate canvas dimensions
    const windowWidth = wx.getSystemInfoSync().windowWidth;
    const canvasSize = windowWidth * 0.7; // Canvas will be 70% of screen width

    this.setData({
      canvasWidth: canvasSize,
      canvasHeight: canvasSize
    });
  },

  initAuthCanvas: function() {
    const query = wx.createSelectorQuery();
    query.select('#auth-gesture-canvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res || !res[0]) return;

        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');

        // 获取canvas的实际渲染尺寸
        const dpr = wx.getSystemInfoSync().pixelRatio;
        canvas.width = this.data.canvasWidth * dpr;
        canvas.height = this.data.canvasHeight * dpr;

        // 设置canvas的样式尺寸
        canvas.style = {
          width: this.data.canvasWidth + 'px',
          height: this.data.canvasHeight + 'px'
        };

        // 缩放绘图上下文，使得1个单位等于1个物理像素
        ctx.scale(dpr, dpr);

        this.canvas = canvas;
        this.ctx = ctx;

        // Initialize the grid points
        this.initializePoints();
        this.drawGestureGrid();
      });
  },

  initializePoints: function() {
    const { gridSize, canvasWidth, canvasHeight, pointRadius } = this.data;
    const points = [];

    // Calculate the space between points
    const spacingX = canvasWidth / gridSize;
    const spacingY = canvasHeight / gridSize;

    // Calculate the offset to center the points
    const offsetX = spacingX / 2;
    const offsetY = spacingY / 2;

    // Create the grid of points
    for (let i = 0; i < gridSize; i++) {
      for (let j = 0; j < gridSize; j++) {
        points.push({
          x: offsetX + j * spacingX,
          y: offsetY + i * spacingY,
          id: i * gridSize + j + 1, // Point ID (1-9)
          touched: false
        });
      }
    }

    this.setData({ points });
  },

  drawGestureGrid: function() {
    const { points, pointRadius, drawnLines, currentLineColor } = this.data;
    const ctx = this.ctx;

    if (!ctx) return;

    // Clear the canvas
    ctx.clearRect(0, 0, this.data.canvasWidth, this.data.canvasHeight);

    // Draw the grid points
    points.forEach(point => {
      ctx.beginPath();
      ctx.arc(point.x, point.y, pointRadius, 0, Math.PI * 2);
      ctx.fillStyle = point.touched ? currentLineColor : '#E0E0E0';
      ctx.fill();
      ctx.strokeStyle = '#CCCCCC';
      ctx.lineWidth = 2;
      ctx.stroke();
    });

    // Draw the connecting lines between touched points
    if (drawnLines.length > 0) {
      ctx.beginPath();
      ctx.moveTo(drawnLines[0].x, drawnLines[0].y);

      for (let i = 1; i < drawnLines.length; i++) {
        ctx.lineTo(drawnLines[i].x, drawnLines[i].y);
      }

      // If there is a last point that is not part of the drawn lines yet
      if (this.data.lastPoint) {
        ctx.lineTo(this.data.lastPoint.x, this.data.lastPoint.y);
      }

      ctx.strokeStyle = currentLineColor;
      ctx.lineWidth = 5;
      ctx.stroke();
    }
  },

  handleAuthTouchStart: function(e) {
    const { points } = this.data;
    const touch = e.touches[0];

    // 获取canvas的位置信息
    const query = wx.createSelectorQuery();
    query.select('#auth-gesture-canvas')
      .boundingClientRect()
      .exec((res) => {
        if (!res || !res[0]) return;

        const canvas = res[0];
        // 计算触摸点相对于canvas的坐标
        const x = touch.clientX - canvas.left;
        const y = touch.clientY - canvas.top;

        // Check if the touch started on a point
        const point = this.findTouchedPoint(x, y);

        if (point) {
          // Mark the point as touched
          const updatedPoints = points.map(p => {
            if (p.id === point.id) {
              return { ...p, touched: true };
            }
            return p;
          });

          // Start drawing lines
          const drawnLines = [{ x: point.x, y: point.y }];

          // Add the point to gesture password
          const authGesturePassword = [...this.data.authGesturePassword, point.id];

          this.setData({
            points: updatedPoints,
            drawnLines,
            lastPoint: null,
            authGesturePassword
          });

          this.drawGestureGrid();
        }
      });
  },

  handleAuthTouchMove: function(e) {
    if (this.data.drawnLines.length === 0) return; // No touch start yet

    const { points } = this.data;
    const touch = e.touches[0];

    // 获取canvas的位置信息
    const query = wx.createSelectorQuery();
    query.select('#auth-gesture-canvas')
      .boundingClientRect()
      .exec((res) => {
        if (!res || !res[0]) return;

        const canvas = res[0];
        // 计算触摸点相对于canvas的坐标
        const x = touch.clientX - canvas.left;
        const y = touch.clientY - canvas.top;

        // Update last point for smooth line drawing
        this.setData({
          lastPoint: { x: x, y: y }
        });

        // Check if we've touched a new point
        const point = this.findTouchedPoint(x, y);

        if (point && !this.isPointAlreadyTouched(point.id)) {
          // Mark the point as touched
          const updatedPoints = points.map(p => {
            if (p.id === point.id) {
              return { ...p, touched: true };
            }
            return p;
          });

          // Add to drawn lines
          const drawnLines = [...this.data.drawnLines, { x: point.x, y: point.y }];

          // Add the point to gesture password
          const authGesturePassword = [...this.data.authGesturePassword, point.id];

          this.setData({
            points: updatedPoints,
            drawnLines,
            authGesturePassword
          });
        }

        this.drawGestureGrid();
      });
  },

  handleAuthTouchEnd: function() {
    const { authGesturePassword } = this.data;

    // Check if gesture is valid (at least 4 points)
    if (authGesturePassword.length < 4) {
      this.setData({
        showAuthError: true,
        authErrorMessage: '手势密码至少需要连接 4 个点',
        currentLineColor: '#FF3B30'
      });

      this.drawGestureGrid();

      // Reset after a delay
      setTimeout(() => {
        this.resetAuthGesture();
      }, 1500);

      return;
    }

    // Verify the gesture password
    try {
      const settings = wx.getStorageSync('appSettings') || {};
      const savedGesture = settings.accessGesturePassword || [];

      if (this.arraysEqual(authGesturePassword, savedGesture)) {
        // Authentication successful
        const currentTime = Date.now();
        this.setData({
          isAuthenticated: true,
          authTimestamp: currentTime,
          currentLineColor: '#34C759',
          showAuthError: false
        });

        this.drawGestureGrid();

        wx.showToast({
          title: '认证成功',
          icon: 'success'
        });

        // 延迟清理手势痕迹并加载密码条目
        setTimeout(() => {
          this.clearGestureTraces();
          this.loadPasswordEntries();
        }, 1000);
      } else {
        // Authentication failed
        this.setData({
          showAuthError: true,
          authErrorMessage: '手势密码错误，请重试',
          currentLineColor: '#FF3B30'
        });

        this.drawGestureGrid();

        // Reset after a delay
        setTimeout(() => {
          this.resetAuthGesture();
        }, 1500);
      }
    } catch (e) {
      console.error('Failed to verify gesture:', e);
      this.setData({
        showAuthError: true,
        authErrorMessage: '认证失败，请重试',
        currentLineColor: '#FF3B30'
      });

      setTimeout(() => {
        this.resetAuthGesture();
      }, 1500);
    }
  },

  findTouchedPoint: function(x, y) {
    const { points, pointRadius } = this.data;

    // Check if the touch is within any point's radius
    for (const point of points) {
      const distance = Math.sqrt(
        Math.pow(point.x - x, 2) + Math.pow(point.y - y, 2)
      );

      if (distance <= pointRadius * 1.5) { // A bit larger area for easier touch
        return point;
      }
    }

    return null;
  },

  isPointAlreadyTouched: function(pointId) {
    return this.data.authGesturePassword.includes(pointId);
  },

  resetAuthGesture: function() {
    // Reset the grid points
    const updatedPoints = this.data.points.map(p => ({ ...p, touched: false }));

    this.setData({
      points: updatedPoints,
      drawnLines: [],
      lastPoint: null,
      authGesturePassword: [],
      showAuthError: false,
      currentLineColor: '#007AFF'
    });

    this.drawGestureGrid();
  },

  clearGestureTraces: function() {
    // 清理手势痕迹，重置所有状态
    const updatedPoints = this.data.points.map(p => ({ ...p, touched: false }));
    
    this.setData({
      points: updatedPoints,
      drawnLines: [],
      lastPoint: null,
      authGesturePassword: [],
      showAuthError: false,
      currentLineColor: '#007AFF'
    });
    
    // 重新绘制干净的网格
    if (this.ctx) {
      this.drawGestureGrid();
    }
  },

  arraysEqual: function(a, b) {
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (a[i] !== b[i]) return false;
    }
    return true;
  }
});
